# Script d'entraînement nnUNet amélioré

## 🎯 Problème résolu

Votre script original utilisait `FOLD = "all"` ce qui signifie que **toutes les données étaient utilisées pour l'entraînement ET la validation** - il n'y avait donc pas de vraie validation indépendante.

## ✅ Solution

Ce script implémente une **validation croisée 5-fold** robuste comme recommandé par la documentation officielle nnUNet.

## 📋 Configuration

Modifiez directement les variables en haut du fichier `train_nnunet.py` :

```python
# === CONFIGURATION (MODIFIEZ ICI) ===
DATASET_ID = "030"                    # Votre dataset
CONFIGURATION = "2d"                  # 2d, 3d_fullres, 3d_lowres
EPOCHS = 750                          # Nombre d'époques
GPU_ID = "0"                          # GPU à utiliser

# === VALIDATION CROISÉE ===
USE_CROSS_VALIDATION = True           # True = 5-fold CV, False = mode "all"
VALIDATION_FOLDS = [0, 1, 2, 3, 4]   # Folds à entraîner

# === CHEMINS (ADAPTEZ À VOTRE ENVIRONNEMENT) ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"
```

## 🚀 Utilisation

1. **Modifiez la configuration** dans `train_nnunet.py`
2. **Lancez l'entraînement complet** :
   ```bash
   python train_nnunet.py
   ```

Le script automatise **tout le processus** :
- ✅ Prétraitement du dataset
- ✅ Entraînement des 5 folds (validation croisée)
- ✅ Recherche de la meilleure configuration
- ✅ Création des fichiers d'ensemble
- ✅ Génération des instructions d'inférence

## 📊 Validation croisée : Avant vs Après

### ❌ Avant (problématique)
```
FOLD = "all"
Entraînement: 100% des données
Validation:   100% des données (identiques!)
Résultat:     Métriques biaisées, surapprentissage non détecté
```

### ✅ Après (correct)
```
USE_CROSS_VALIDATION = True
Fold 0: Train 80% | Val 20% (cases 0-19)
Fold 1: Train 80% | Val 20% (cases 20-39)  
Fold 2: Train 80% | Val 20% (cases 40-59)
Fold 3: Train 80% | Val 20% (cases 60-79)
Fold 4: Train 80% | Val 20% (cases 80-99)

Résultat: Métriques fiables avec validation indépendante
```

## 🔧 Exemples de configuration

### Configuration rapide pour test
```python
DATASET_ID = "030"
EPOCHS = 50                           # Réduit pour test
USE_CROSS_VALIDATION = False          # Mode rapide
```

### Configuration de production
```python
DATASET_ID = "030"
EPOCHS = 1000                         # Plus d'époques
USE_CROSS_VALIDATION = True           # Validation croisée complète
VALIDATION_FOLDS = [0, 1, 2, 3, 4]   # 5 folds
```

### Configuration multi-GPU
```python
GPU_ID = "0,1,2,3"                    # 4 GPUs
NUM_GPUS = 4
```

### Configuration Windows
```python
RAW_PATH = "C:/Data/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "C:/Data/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "C:/Data/nnUnet/nnUnet_results"
```

## 📁 Structure des résultats

```
logs/                                 # Logs d'entraînement
└── nnunet_training_030_20240101_120000.log

RESULTS_PATH/
└── Dataset030_YourDataset/
    ├── nnUNetTrainer_750epochs__nnUNetPlans__2d/
    │   ├── fold_0/                   # Modèle fold 0
    │   ├── fold_1/                   # Modèle fold 1
    │   ├── fold_2/                   # Modèle fold 2
    │   ├── fold_3/                   # Modèle fold 3
    │   └── fold_4/                   # Modèle fold 4
    ├── inference_instructions.txt    # 🆕 Instructions d'inférence
    ├── inference_information.json    # 🆕 Informations détaillées
    └── postprocessing.pkl            # 🆕 Post-processing optimisé
```

## 🔍 Monitoring

### Logs en temps réel
```bash
tail -f logs/nnunet_training_030_*.log
```

### Vérification des résultats
Chaque fold génère :
- `checkpoint_final.pth` : Modèle final
- `validation/summary.json` : Métriques de validation
- `progress.png` : Courbes d'entraînement

### Fichiers d'ensemble générés automatiquement
- `inference_instructions.txt` : Commandes exactes pour l'inférence
- `inference_information.json` : Performances de chaque configuration
- `postprocessing.pkl` : Post-processing optimisé

## 💡 Avantages de cette version

1. ✅ **Validation robuste** : 5-fold cross-validation au lieu de `FOLD="all"`
2. ✅ **Métriques fiables** : Validation indépendante pour chaque fold
3. ✅ **Configuration simple** : Tout en haut du fichier
4. ✅ **Logging automatique** : Suivi dans `logs/`
5. ✅ **Gestion d'erreurs** : Messages clairs en cas de problème
6. ✅ **Compatible nnUNet v2** : Suit les bonnes pratiques officielles

## 🚨 Migration depuis l'ancien script

Si vous aviez `FOLD = "all"` dans votre ancien script :

1. **Sauvegardez** votre ancien script
2. **Remplacez** par ce nouveau script
3. **Modifiez** la configuration en haut du fichier
4. **Lancez** : `python train_nnunet.py`

## ⚙️ Dépannage

### Erreurs courantes
- **Dataset non trouvé** : Vérifiez `RAW_PATH` et `DATASET_ID`
- **GPU non disponible** : Vérifiez `GPU_ID`
- **Mémoire insuffisante** : Réduisez `NUM_GPUS` ou utilisez un GPU plus puissant
- **Trainer non trouvé** : Vérifiez que `nnUNetTrainer_XXXepochs` existe dans votre installation

### Support
- Logs détaillés dans `logs/`
- Messages d'erreur explicites dans la console
- Vérification automatique de l'intégrité du dataset

## 🎉 Résultat attendu

Avec ce script, vous obtiendrez :
- **5 modèles** entraînés (un par fold)
- **Métriques de validation** fiables pour chaque fold
- **Estimation robuste** des performances réelles
- **Possibilité d'ensemble** des 5 modèles pour de meilleures prédictions

La validation croisée vous donne une estimation beaucoup plus fiable des performances de votre modèle sur des données non vues.
