import os
import subprocess

# === CONFIGURATION (MODIFIEZ ICI) ===
DATASET_ID = "029"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5                # Doit correspondre au modèle entraîné
FOLDS = "0 1 2 3 4"       # Folds à exporter (séparés par des espaces)
OUTPUT_ZIP = f"model_{DATASET_ID}_{CONFIGURATION}_folds0to4.zip"

# === CHEMINS (MÊME CONFIG QUE train_nnunet.py) ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH

# === CONSTRUCTION DE LA COMMANDE ===
trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"

cmd = (
    f"nnUNetv2_export_model_to_zip "
    f"-d {DATASET_ID} "
    f"-o {OUTPUT_ZIP} "
    f"-c {CONFIGURATION} "
    f"-tr {trainer_class} "
    f"-p {PLANS_NAME} "
    f"-f {FOLDS}"
)

# === AFFICHAGE ET EXÉCUTION ===
print("Commande générée :")
print(cmd)
print(f"\nFichier de sortie : {OUTPUT_ZIP}")
print("\nExportation en cours...\n")

try:
    subprocess.run(cmd, shell=True, check=True)
    print(f"\nExportation réussie : {OUTPUT_ZIP}")
    
    # Vérifier la taille du fichier
    if os.path.exists(OUTPUT_ZIP):
        file_size = os.path.getsize(OUTPUT_ZIP) / (1024 * 1024)  # MB
        print(f"Taille du fichier : {file_size:.1f} MB")
    
except subprocess.CalledProcessError as e:
    print(f"\nÉchec de l'exportation.")
    print(f"Erreur : {e}")
    print(f"Code de retour : {e.returncode}")

print(f"\nPour installer sur un autre système :")
print(f"nnUNetv2_install_pretrained_model_from_zip {OUTPUT_ZIP}")
