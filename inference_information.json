{"all_results": {"nnUNetTrainer_5epochs__nnUNetPlans__2d": 0.04793786685637424}, "best_model_or_ensemble": {"postprocessing_file": "/mnt/results/nnUnet_results/Dataset029_TPI_calibprod_light_complet/nnUNetTrainer_5epochs__nnUNetPlans__2d/crossval_results_folds_0_1_2_3_4/postprocessing.pkl", "result_on_crossval_post_pp": 0.04912251722007812, "result_on_crossval_pre_pp": 0.04793786685637424, "selected_model_or_models": [{"configuration": "2d", "plans_identifier": "nnUNetPlans", "trainer": "nnUNetTrainer_5epochs"}], "some_plans_file": "/mnt/results/nnUnet_results/Dataset029_TPI_calibprod_light_complet/nnUNetTrainer_5epochs__nnUNetPlans__2d/crossval_results_folds_0_1_2_3_4/plans.json"}, "considered_models": [{"configuration": "2d", "plans": "nnUNetPlans", "trainer": "nnUNetTrainer_5epochs"}], "dataset_name_or_id": "Dataset029_TPI_calibprod_light_complet", "ensembling_allowed": true, "folds": [0, 1, 2, 3, 4]}