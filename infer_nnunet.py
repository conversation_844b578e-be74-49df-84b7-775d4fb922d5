import os
import subprocess
import time
import logging
from pathlib import Path

# === CONFIGURATION (MODIFIEZ ICI) ===
DATASET_ID = "029"
CONFIGURATION = "2d"      # '2d', '3d_fullres', '3d_lowres'
PLANS_NAME = "nnUNetPlans"
EPOCHS = 5                # Doit correspondre au modèle entraîné
GPU_ID = "0"              # "0" ou "0,1,2,3" pour multi-GPU

# === DOSSIERS D'INFÉRENCE ===
INPUT_FOLDER = "/path/to/your/input/images"     # MODIFIEZ : Dossier contenant les images à prédire
OUTPUT_FOLDER = "/path/to/your/output/results"  # MODIFIEZ : Dossier de sortie des prédictions

# === CHEMINS nnUNet (MÊME CONFIG QUE train_nnunet.py) ===
RAW_PATH = "/mnt/Datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH = "/mnt/Datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH = "/mnt/results/nnUnet_results"

# === CONFIGURATION ENVIRONNEMENT ===
os.environ["nnUNet_raw"] = RAW_PATH
os.environ["nnUNet_preprocessed"] = PREPROCESSED_PATH
os.environ["nnUNet_results"] = RESULTS_PATH
os.environ["CUDA_VISIBLE_DEVICES"] = GPU_ID

# === LOGGING SIMPLE ===
def setup_logging():
    """Configure le logging simple"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    timestamp = time.strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"nnunet_inference_{DATASET_ID}_{timestamp}.log"

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

def run_command(cmd: str, description: str = "") -> bool:
    """Exécute une commande avec gestion d'erreurs"""
    logger.info(f"🟢 {description if description else 'Lancement'}: {cmd}")

    try:
        subprocess.run(cmd, shell=True, check=True)
        logger.info(f"✅ Succès: {description}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Erreur lors de: {description}")
        logger.error(f"Code de retour: {e.returncode}")
        return False

def find_inference_files():
    """Trouve les fichiers d'inférence générés par train_nnunet.py"""
    import glob
    dataset_folder = os.path.join(RESULTS_PATH, f"Dataset{DATASET_ID}_*")
    folders = glob.glob(dataset_folder)

    if not folders:
        logger.error(f"❌ Dossier dataset non trouvé: {dataset_folder}")
        return None, None

    dataset_path = folders[0]
    inference_instructions = os.path.join(dataset_path, "inference_instructions.txt")
    inference_information = os.path.join(dataset_path, "inference_information.json")

    return inference_instructions, inference_information

def check_model_exists() -> bool:
    """Vérifie que le modèle entraîné existe et lit les instructions d'inférence"""
    inference_instructions, inference_information = find_inference_files()

    if inference_instructions and os.path.exists(inference_instructions):
        logger.info(f"✅ Fichier d'instructions trouvé: {inference_instructions}")

        # Lire les instructions d'inférence
        try:
            with open(inference_instructions, 'r') as f:
                instructions = f.read()
            logger.info("📋 Instructions d'inférence disponibles:")
            logger.info(instructions[:200] + "..." if len(instructions) > 200 else instructions)
        except Exception as e:
            logger.warning(f"⚠️  Impossible de lire les instructions: {e}")

        return True
    else:
        # Fallback: vérification manuelle
        trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
        model_folder = os.path.join(RESULTS_PATH, f"Dataset{DATASET_ID}_*",
                                   f"{trainer_class}__{PLANS_NAME}__{CONFIGURATION}")
        import glob
        folders = glob.glob(model_folder)

        if not folders:
            logger.error(f"❌ Modèle non trouvé: {model_folder}")
            logger.error("💡 Assurez-vous d'avoir entraîné le modèle avec train_nnunet.py")
            return False

        logger.info(f"✅ Modèle trouvé: {folders[0]}")
        logger.warning("⚠️  Fichiers d'instructions non trouvés, utilisation de la configuration manuelle")
        return True

def check_input_folder() -> bool:
    """Vérifie que le dossier d'entrée existe et contient des images"""
    if not os.path.exists(INPUT_FOLDER):
        logger.error(f"❌ Dossier d'entrée non trouvé: {INPUT_FOLDER}")
        return False

    # Compter les images compatibles nnUNet
    image_files = [f for f in os.listdir(INPUT_FOLDER)
                   if f.endswith(('.nii.gz', '.nii', '.png', '.tif', '.tiff'))]

    if not image_files:
        logger.error(f"❌ Aucune image trouvée dans: {INPUT_FOLDER}")
        logger.error("💡 Formats supportés: .nii.gz, .nii, .png, .tif, .tiff")
        return False

    logger.info(f"✅ {len(image_files)} images trouvées dans le dossier d'entrée")
    return True

def get_optimal_inference_command():
    """Récupère la commande d'inférence optimale depuis inference_instructions.txt"""
    inference_instructions, inference_information = find_inference_files()

    if inference_instructions and os.path.exists(inference_instructions):
        try:
            with open(inference_instructions, 'r') as f:
                content = f.read()

            # Extraire la commande nnUNetv2_predict
            lines = content.split('\n')
            for line in lines:
                if 'nnUNetv2_predict' in line and '-i' in line and '-o' in line:
                    # Remplacer les dossiers par nos dossiers
                    cmd = line.strip()
                    # Remplacer les chemins génériques par nos chemins
                    cmd = cmd.replace('-i INPUT_FOLDER', f'-i "{INPUT_FOLDER}"')
                    cmd = cmd.replace('-o OUTPUT_FOLDER', f'-o "{OUTPUT_FOLDER}"')
                    logger.info("✅ Utilisation de la commande optimale depuis inference_instructions.txt")
                    return cmd
        except Exception as e:
            logger.warning(f"⚠️  Erreur lecture instructions: {e}")

    # Fallback: commande manuelle
    logger.info("⚠️  Utilisation de la commande manuelle")
    trainer_class = f"nnUNetTrainer_{EPOCHS}epochs"
    return f'nnUNetv2_predict -i "{INPUT_FOLDER}" -o "{OUTPUT_FOLDER}" -d {DATASET_ID} -c {CONFIGURATION} -tr {trainer_class}'

def run_inference() -> bool:
    """Exécute l'inférence nnUNet"""
    # Créer le dossier de sortie
    os.makedirs(OUTPUT_FOLDER, exist_ok=True)

    # Obtenir la commande optimale
    cmd = get_optimal_inference_command()

    success = run_command(cmd, "Inférence nnUNet avec configuration optimale")

    if success:
        logger.info(f"📁 Résultats sauvés dans: {OUTPUT_FOLDER}")

        # Lister les fichiers générés
        if os.path.exists(OUTPUT_FOLDER):
            result_files = os.listdir(OUTPUT_FOLDER)
            logger.info(f"📊 {len(result_files)} fichiers générés:")
            for f in result_files[:5]:  # Afficher les 5 premiers
                logger.info(f"   - {f}")
            if len(result_files) > 5:
                logger.info(f"   ... et {len(result_files) - 5} autres")

        # Vérifier si post-processing est nécessaire
        check_postprocessing_needed()

    return success

def check_postprocessing_needed():
    """Vérifie si un post-processing est nécessaire"""
    inference_instructions, _ = find_inference_files()

    if inference_instructions and os.path.exists(inference_instructions):
        try:
            with open(inference_instructions, 'r') as f:
                content = f.read()

            if 'nnUNetv2_apply_postprocessing' in content:
                logger.info("💡 Post-processing recommandé:")
                lines = content.split('\n')
                for line in lines:
                    if 'nnUNetv2_apply_postprocessing' in line:
                        logger.info(f"   {line.strip()}")
        except Exception as e:
            logger.warning(f"⚠️  Erreur lecture post-processing: {e}")

def main():
    """Fonction principale d'inférence"""
    start_time = time.time()

    try:
        logger.info("🔮 Début de l'inférence nnUNet")
        logger.info(f"📋 Configuration:")
        logger.info(f"   - Dataset ID: {DATASET_ID}")
        logger.info(f"   - Configuration: {CONFIGURATION}")
        logger.info(f"   - Époques: {EPOCHS}")
        logger.info(f"   - Trainer: nnUNetTrainer_{EPOCHS}epochs")
        logger.info(f"   - GPU: {GPU_ID}")
        logger.info(f"   - Input: {INPUT_FOLDER}")
        logger.info(f"   - Output: {OUTPUT_FOLDER}")

        # 1. Vérifications préliminaires
        if not check_model_exists():
            return False

        if not check_input_folder():
            return False

        # 2. Exécuter l'inférence
        success = run_inference()

        if not success:
            logger.error("❌ Échec de l'inférence")
            return False

        # Temps total
        total_time = time.time() - start_time
        logger.info(f"🎉 Inférence terminée en {total_time/60:.2f} minutes")

        return True

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
